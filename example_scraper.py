import requests
from bs4 import BeautifulSoup
import time

def scrape_example_site():
    # Приклад парсингу новинного сайту
    url = "https://www.pornhub.com/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Знаходимо заголовки
    titles = soup.find_all('h1')
    for title in titles:
        print(title.get_text().strip())