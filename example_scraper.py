import requests
from bs4 import BeautifulSoup
import time
import sys

def scrape_example_site():
    """Приклад парсингу новинного сайту з обробкою помилок"""
    # Використовуємо більш доступний сайт для тестування
    url = "https://httpbin.org/html"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    try:
        print(f"Відправляємо запит до: {url}")
        response = requests.get(url, headers=headers, timeout=10)

        # Перевіряємо статус відповіді
        print(f"Статус відповіді: {response.status_code}")

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Знаходимо заголовки
            titles = soup.find_all(['h1', 'h2', 'h3'])
            print(f"Знайдено {len(titles)} заголовків:")

            for i, title in enumerate(titles, 1):
                print(f"{i}. {title.get_text().strip()}")

            # Також виведемо весь текст для перевірки
            print("\n--- Весь текст сторінки ---")
            print(soup.get_text().strip())

        else:
            print(f"Помилка: отримано статус {response.status_code}")
            print(f"Відповідь: {response.text[:200]}...")

    except requests.exceptions.RequestException as e:
        print(f"Помилка при виконанні запиту: {e}")
    except Exception as e:
        print(f"Загальна помилка: {e}")

def scrape_news_site():
    """Приклад парсингу реального новинного сайту"""
    # Використовуємо BBC News як приклад
    url = "https://www.bbc.com/news"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }

    try:
        print(f"Відправляємо запит до: {url}")
        response = requests.get(url, headers=headers, timeout=15)

        print(f"Статус відповіді: {response.status_code}")

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Шукаємо заголовки новин (специфічні для BBC)
            headlines = soup.find_all(['h2', 'h3'], class_=lambda x: x and ('headline' in x.lower() or 'title' in x.lower()))

            if not headlines:
                # Якщо не знайшли специфічні класи, шукаємо всі h2 та h3
                headlines = soup.find_all(['h2', 'h3'])

            print(f"Знайдено {len(headlines)} заголовків:")

            for i, headline in enumerate(headlines[:10], 1):  # Показуємо перші 10
                text = headline.get_text().strip()
                if text:  # Пропускаємо порожні заголовки
                    print(f"{i}. {text}")

        else:
            print(f"Помилка: отримано статус {response.status_code}")

    except requests.exceptions.RequestException as e:
        print(f"Помилка при виконанні запиту: {e}")
    except Exception as e:
        print(f"Загальна помилка: {e}")

if __name__ == "__main__":
    print("=== Тестування парсингу ===")
    print("\n1. Тестовий сайт:")
    scrape_example_site()

    print("\n" + "="*50)
    print("\n2. Новинний сайт:")
    scrape_news_site()