import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
import platform
import shutil
import datetime
import threading
from PIL import Image, ImageTk
import io
import subprocess

class FileExplorer:
    def __init__(self, root):
        self.root = root
        self.root.title("Файловий провідник")
        self.root.geometry("1200x700")
        
        # Налаштування стилів
        self.setup_styles()
        
        # Поточний шлях та історія
        self.current_path = self.get_this_pc_path()
        self.history = []
        self.history_index = -1
        
        # Змінні для пошуку та фільтрації
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        self.filter_var = tk.StringVar(value="Всі файли")
        
        # Змінні для сортування
        self.sort_column = "#0"
        self.sort_reverse = False
        
        # Буфер обміну
        self.clipboard_files = []
        self.clipboard_mode = "copy"  # "copy" або "cut"
        
        # Створення головного контейнера
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Створення верхньої панелі з навігацією
        self.create_top_panel()
        
        # Створення трьох панелей однакової висоти
        self.create_panels()
        
        # Створення вмісту панелей
        self.create_left_panel()
        self.create_center_panel()
        self.create_right_panel()
        
        # Перевіряємо, чи всі елементи створені
        print("Інтерфейс створено:")
        print(f"- Ліва панель: {hasattr(self, 'left_frame')}")
        print(f"- Центральна панель: {hasattr(self, 'center_frame')}")
        print(f"- Права панель: {hasattr(self, 'right_frame')}")
        print(f"- Дерево: {hasattr(self, 'tree')}")
        print(f"- Список файлів: {hasattr(self, 'file_tree')}")
        print(f"- Адресний рядок: {hasattr(self, 'address_entry')}")
        print(f"- Пошук: {hasattr(self, 'search_entry')}")
        print(f"- Статусний рядок: {hasattr(self, 'status_label')}")
        
        # Створення статусного рядка
        self.create_status_bar()
        
        # Завантаження початкового стану
        self.load_directory_tree()
        self.load_current_directory()
        
        # Прив'язка події зміни розміру вікна
        self.root.bind("<Configure>", self.on_window_resize)
        
        # Прив'язка події зміни розміру canvas
        self.image_canvas.bind("<Configure>", self.on_canvas_resize)
    
    def setup_styles(self):
        """Налаштування стилів"""
        style = ttk.Style()
        
        # Стилі для заголовків
        style.configure("Title.TLabel", 
                       font=("Segoe UI", 8, "bold"),
                       padding=(2, 1))
        
        style.configure("Path.TLabel", 
                       font=("Segoe UI", 8),
                       padding=(2, 1))
        
        # Стиль для панелей
        style.configure("Panel.TFrame", 
                       relief="flat", 
                       borderwidth=1)
        
        # Стиль для кнопок
        style.configure("Nav.TButton",
                       font=("Segoe UI", 8),
                       padding=(4, 1))
        
        # Стиль для адресного рядка
        style.configure("Address.TEntry",
                       font=("Segoe UI", 8),
                       padding=(2, 1))
        
        # Стиль для пошуку
        style.configure("Search.TEntry",
                       font=("Segoe UI", 8),
                       padding=(2, 1))
        
        # Стиль для контейнера попереднього перегляду
        style.configure("Preview.TFrame",
                       relief="sunken",
                       borderwidth=1)
        
        # Стиль для підказки
        style.configure("Hint.TLabel",
                       font=("Segoe UI", 8),
                       foreground="gray",
                       padding=(10, 20))
    
    def get_this_pc_path(self):
        """Отримання шляху до 'Цей ПК' в залежності від операційної системи"""
        if platform.system() == "Windows":
            return "C:\\"
        else:
            return "/"
    
    def create_top_panel(self):
        """Створення верхньої панелі з навігацією"""
        self.top_frame = ttk.Frame(self.main_frame, style="Panel.TFrame")
        self.top_frame.pack(fill=tk.X, padx=1, pady=1)
        
        # Кнопки навігації
        ttk.Button(self.top_frame, text="← Назад", command=self.go_back, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(self.top_frame, text="↑ Вгору", command=self.go_up, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(self.top_frame, text="⟳ Оновити", command=self.refresh, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        
        # Роздільник
        ttk.Separator(self.top_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=2)
        
        # Адресний рядок
        address_frame = ttk.Frame(self.top_frame)
        address_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=1)
        
        ttk.Label(address_frame, text="Адреса:", style="Path.TLabel").pack(side=tk.LEFT, padx=1)
        self.address_entry = ttk.Entry(address_frame, style="Address.TEntry")
        self.address_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=1)
        self.address_entry.bind("<Return>", self.on_address_enter)
        
        # Пошук
        ttk.Label(address_frame, text="Пошук:", style="Path.TLabel").pack(side=tk.LEFT, padx=1)
        self.search_entry = ttk.Entry(address_frame, textvariable=self.search_var, style="Search.TEntry", width=15)
        self.search_entry.pack(side=tk.LEFT, padx=1)
        
        # Фільтр
        filter_combo = ttk.Combobox(address_frame, textvariable=self.filter_var, values=["Всі файли", "Тільки папки", "Тільки файли"], width=12, state="readonly")
        filter_combo.pack(side=tk.LEFT, padx=1)
        filter_combo.bind("<<ComboboxSelected>>", self.on_filter_change)
    
    def create_panels(self):
        """Створення трьох панелей однакової висоти"""
        # Створюємо контейнер для панелей
        panels_frame = ttk.Frame(self.main_frame)
        panels_frame.pack(fill=tk.BOTH, expand=True, pady=1)
        
        # Створюємо три панелі
        self.left_frame = ttk.Frame(panels_frame, style="Panel.TFrame")
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 1))
        
        self.center_frame = ttk.Frame(panels_frame, style="Panel.TFrame")
        self.center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1)
        
        self.right_frame = ttk.Frame(panels_frame, style="Panel.TFrame")
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=(1, 0))
        
        # Встановлюємо ширину бокових панелей
        self.root.update_idletasks()
        window_width = self.root.winfo_width()
        panel_width = window_width // 6
        
        self.left_frame.configure(width=panel_width)
        self.right_frame.configure(width=panel_width)
        
        self.left_frame.pack_propagate(False)
        self.right_frame.pack_propagate(False)
    
    def create_left_panel(self):
        """Створення лівої панелі з деревом директорій"""
        # Заголовок
        title_label = ttk.Label(self.left_frame, text="Дерево директорій", style="Title.TLabel")
        title_label.pack(pady=1)
        
        # Роздільник після заголовка
        ttk.Separator(self.left_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=2, pady=1)
        
        # Дерево директорій
        self.tree_frame = ttk.Frame(self.left_frame)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        self.tree = ttk.Treeview(self.tree_frame, show="tree")
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Скролбар для дерева
        tree_scrollbar = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # Прив'язка події вибору
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
    
    def create_center_panel(self):
        """Створення центральної панелі з вмістом директорії"""
        # Кнопки керування під адресою
        control_frame = ttk.Frame(self.center_frame)
        control_frame.pack(fill=tk.X, padx=2, pady=1)
        
        # Кнопки дій
        ttk.Button(control_frame, text="📁 Нова папка", command=self.create_folder, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="📄 Новий файл", command=self.create_file, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        
        # Роздільник
        ttk.Separator(control_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=2)
        
        ttk.Button(control_frame, text="✂️ Вирізати", command=self.cut_files, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="📋 Копіювати", command=self.copy_files, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="📌 Вставити", command=self.paste_files, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="🗑️ Видалити", command=self.delete_files, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        
        # Роздільник
        ttk.Separator(control_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=2)
        
        # Додаткові кнопки
        ttk.Button(control_frame, text="🔄 Перейменувати", command=self.rename_selected, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        ttk.Button(control_frame, text="📖 Властивості", command=self.show_properties, style="Nav.TButton").pack(side=tk.LEFT, padx=1)
        
        # Список файлів
        list_frame = ttk.Frame(self.center_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Створення Treeview для файлів
        columns = ("Розмір", "Дата зміни", "Тип")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings")
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Налаштування колонок
        self.file_tree.heading("#0", text="Ім'я")
        self.file_tree.column("#0", width=120, minwidth=80)
        for col in columns:
            self.file_tree.heading(col, text=col)
            self.file_tree.column(col, width=70, minwidth=50)
        
        # Скролбари
        file_scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        file_scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        file_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.file_tree.configure(yscrollcommand=file_scrollbar_y.set, xscrollcommand=file_scrollbar_x.set)
        
        # Прив'язка події подвійного кліку
        self.file_tree.bind("<Double-1>", self.on_file_double_click)
        self.file_tree.bind("<<TreeviewSelect>>", self.on_file_select)
        self.file_tree.bind("<Button-3>", self.show_context_menu)
        self.file_tree.bind("<Control-a>", self.select_all)
        self.file_tree.bind("<Control-c>", self.copy_files)
        self.file_tree.bind("<Control-x>", self.cut_files)
        self.file_tree.bind("<Control-v>", self.paste_files)
        self.file_tree.bind("<Delete>", self.delete_files)
        
        # Контекстне меню
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Відкрити", command=self.open_selected)
        self.context_menu.add_command(label="Відкрити з...", command=self.open_with)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Копіювати", command=self.copy_files)
        self.context_menu.add_command(label="Вирізати", command=self.cut_files)
        self.context_menu.add_command(label="Вставити", command=self.paste_files)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Перейменувати", command=self.rename_selected)
        self.context_menu.add_command(label="Видалити", command=self.delete_files)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Властивості", command=self.show_properties)
    
    def create_right_panel(self):
        """Створення правої панелі з попереднім переглядом"""
        # Заголовок
        title_label = ttk.Label(self.right_frame, text="Попередній перегляд", style="Title.TLabel")
        title_label.pack(pady=1)
        
        # Роздільник після заголовка
        ttk.Separator(self.right_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=2, pady=1)
        
        # Контейнер для попереднього перегляду з рамкою
        preview_container = ttk.Frame(self.right_frame, style="Preview.TFrame")
        preview_container.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Текстовий віджет для текстових файлів
        self.text_preview = tk.Text(preview_container, wrap=tk.WORD, state=tk.DISABLED, 
                                   font=("Consolas", 8), bg="white", fg="black",
                                   selectbackground="lightblue", selectforeground="black")
        self.text_preview.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        # Canvas для зображень
        self.image_canvas = tk.Canvas(preview_container, bg="white", highlightthickness=0,
                                     relief="flat", borderwidth=0)
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        # Зберігаємо поточне зображення для перемалювання
        self.current_image = None
        self.current_photo = None
        
        # Скролбари для тексту
        text_scrollbar_y = ttk.Scrollbar(preview_container, orient=tk.VERTICAL, command=self.text_preview.yview)
        text_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        text_scrollbar_x = ttk.Scrollbar(preview_container, orient=tk.HORIZONTAL, command=self.text_preview.xview)
        text_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.text_preview.configure(yscrollcommand=text_scrollbar_y.set, xscrollcommand=text_scrollbar_x.set)
        
        # Приховуємо canvas спочатку
        self.image_canvas.pack_forget()
        
        # Додаємо підказку
        self.preview_hint = ttk.Label(preview_container, text="Виберіть файл для перегляду", 
                                     style="Hint.TLabel")
        self.preview_hint.pack(expand=True)
    
    def load_directory_tree(self):
        """Завантаження дерева директорій"""
        self.tree.delete(*self.tree.get_children())
        
        # Додаємо кореневі директорії
        if platform.system() == "Windows":
            drives = self.get_windows_drives()
            for drive in drives:
                self.tree.insert("", "end", drive, text=drive, values=("Диск",))
        else:
            self.tree.insert("", "end", "/", text="/", values=("Коренева директорія",))
    
    def get_windows_drives(self):
        """Отримання списку дисків Windows"""
        drives = []
        for letter in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                drives.append(drive)
        return drives
    
    def load_current_directory(self):
        """Завантаження вмісту поточної директорії"""
        self.file_tree.delete(*self.file_tree.get_children())
        
        # Оновлюємо адресний рядок
        self.address_entry.delete(0, tk.END)
        self.address_entry.insert(0, self.current_path)
        
        try:
            items = os.listdir(self.current_path)
            
            # Розділяємо на директорії та файли
            directories = []
            files = []
            
            for item in items:
                item_path = os.path.join(self.current_path, item)
                if os.path.isdir(item_path):
                    directories.append(item)
                else:
                    files.append(item)
            
            # Застосовуємо фільтр
            filter_value = self.filter_var.get()
            if filter_value == "Тільки папки":
                files = []
            elif filter_value == "Тільки файли":
                directories = []
            
            # Застосовуємо пошук
            search_query = self.search_var.get().lower()
            if search_query:
                directories = [d for d in directories if search_query in d.lower()]
                files = [f for f in files if search_query in f.lower()]
            
            # Сортуємо
            directories.sort()
            files.sort()
            
            # Додаємо директорії
            for directory in directories:
                dir_path = os.path.join(self.current_path, directory)
                try:
                    stat = os.stat(dir_path)
                    self.file_tree.insert("", "end", dir_path, text=directory, 
                                        values=("", stat.st_mtime, "Папка"),
                                        tags=("directory",))
                except:
                    pass
            
            # Додаємо файли
            for file in files:
                file_path = os.path.join(self.current_path, file)
                try:
                    stat = os.stat(file_path)
                    size = self.format_size(stat.st_size)
                    self.file_tree.insert("", "end", file_path, text=file,
                                        values=(size, stat.st_mtime, "Файл"),
                                        tags=("file",))
                except:
                    pass
            
            # Оновлюємо статусний рядок
            self.update_status_bar()
                    
        except PermissionError:
            messagebox.showerror("Помилка", "Немає доступу до цієї директорії")
        except Exception as e:
            messagebox.showerror("Помилка", f"Помилка завантаження директорії: {e}")
    
    def format_size(self, size):
        """Форматування розміру файлу"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def on_tree_select(self, event):
        """Обробка вибору в дереві директорій"""
        selection = self.tree.selection()
        if selection:
            selected_path = selection[0]
            if os.path.isdir(selected_path):
                self.current_path = selected_path
                self.add_to_history(selected_path)
                self.load_current_directory()
    
    def on_file_double_click(self, event):
        """Обробка подвійного кліку по файлу"""
        selection = self.file_tree.selection()
        if selection:
            selected_path = selection[0]
            if os.path.isdir(selected_path):
                self.current_path = selected_path
                self.add_to_history(selected_path)
                self.load_current_directory()
    
    def on_file_select(self, event):
        """Обробка вибору файлу"""
        selection = self.file_tree.selection()
        if selection:
            selected_path = selection[0]
            if os.path.isfile(selected_path):
                self.preview_file(selected_path)
        
        # Оновлюємо статусний рядок
        self.update_status_bar()
    
    def preview_file(self, file_path):
        """Попередній перегляд файлу"""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        # Список підтримуваних форматів зображень
        image_extensions = [
            '.3fr', '.aai', '.ai', '.art', '.arw', '.avs', '.bgr', '.bgra', '.bie', '.bmp',
            '.cal', '.cals', '.canvas', '.cin', '.cmyk', '.cmyka', '.cr2', '.crw', '.cur',
            '.dcm', '.dcr', '.dcx', '.dds', '.dib', '.djvu', '.dng', '.dpx', '.epdf', '.eps',
            '.epsf', '.epsi', '.ept', '.erf', '.exr', '.fax', '.fits', '.fractal', '.fts',
            '.g3', '.gif', '.gif87', '.gray', '.group4', '.hdr', '.hrz', '.icb', '.ico',
            '.icon', '.iiq', '.jbg', '.jbig', '.jng', '.jnx', '.jp2', '.jpe', '.jpeg',
            '.jpg', '.json', '.k25', '.kdc', '.mac', '.mat', '.mef', '.miff', '.mng',
            '.mono', '.mpc', '.mrw', '.mtv', '.nef', '.nrw', '.orf', '.otb', '.otf',
            '.pal', '.palm', '.pam', '.pbm', '.pcd', '.pcds', '.pct', '.pcx', '.pdb',
            '.pdf', '.pdfa', '.pef', '.pes', '.pfm', '.pgm', '.picon', '.pict', '.pix',
            '.pjpeg', '.plasma', '.png', '.png00', '.png8', '.png24', '.png32', '.png48',
            '.png64', '.pnm', '.ps', '.psb', '.psd', '.ptif', '.pwp', '.r', '.raf',
            '.ras', '.raw', '.rgb', '.rgba', '.rgbo', '.rgf', '.rla', '.rle', '.rmf',
            '.rw2', '.sfw', '.sgi', '.six', '.sixel', '.sr2', '.srf', '.stegano', '.sun',
            '.tga', '.tif', '.tiff', '.tiff64', '.tile', '.uyvy', '.vda', '.vicar',
            '.viff', '.vips', '.vst', '.wbmp', '.webp', '.wpg', '.xbm', '.xcf', '.xwd', '.x3f'
        ]
        
        try:
            if file_extension == '.txt':
                self.preview_text_file(file_path)
            elif file_extension in image_extensions:
                self.preview_image_file(file_path)
            else:
                self.clear_preview()
        except Exception as e:
            self.clear_preview()
            messagebox.showerror("Помилка", f"Помилка перегляду файлу: {e}")
    
    def preview_text_file(self, file_path):
        """Попередній перегляд текстового файлу"""
        # Приховуємо підказку
        self.preview_hint.pack_forget()
        
        # Показуємо текстовий віджет
        self.image_canvas.pack_forget()
        self.text_preview.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        # Очищаємо попередній вміст
        self.text_preview.config(state=tk.NORMAL)
        self.text_preview.delete(1.0, tk.END)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                self.text_preview.insert(1.0, content)
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='cp1251') as file:
                    content = file.read()
                    self.text_preview.insert(1.0, content)
            except:
                self.text_preview.insert(1.0, "Файл не може бути прочитаний як текст")
        
        self.text_preview.config(state=tk.DISABLED)
    
    def preview_image_file(self, file_path):
        """Попередній перегляд зображення"""
        # Приховуємо підказку
        self.preview_hint.pack_forget()
        
        # Показуємо canvas
        self.text_preview.pack_forget()
        self.image_canvas.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        try:
            # Відкриваємо зображення
            self.current_image = Image.open(file_path)
            
            # Зберігаємо оригінальне зображення та малюємо його
            self.redraw_image()
            
        except Exception as e:
            self.image_canvas.delete("all")
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()
            if canvas_width <= 1:
                canvas_width = 200
            if canvas_height <= 1:
                canvas_height = 150
            self.image_canvas.create_text(canvas_width // 2, canvas_height // 2, 
                                        text=f"Помилка завантаження\nзображення:\n{e}", 
                                        anchor=tk.CENTER, fill="red")
    
    def clear_preview(self):
        """Очищення попереднього перегляду"""
        self.text_preview.config(state=tk.NORMAL)
        self.text_preview.delete(1.0, tk.END)
        self.text_preview.config(state=tk.DISABLED)
        self.image_canvas.delete("all")
        self.current_image = None
        self.current_photo = None
        
        # Показуємо підказку
        self.text_preview.pack_forget()
        self.image_canvas.pack_forget()
        self.preview_hint.pack(expand=True)
    
    def go_back(self):
        """Перехід назад"""
        if self.history_index > 0:
            self.history_index -= 1
            self.current_path = self.history[self.history_index]
            self.load_current_directory()
    
    def go_up(self):
        """Перехід на рівень вище"""
        parent = os.path.dirname(self.current_path)
        if parent != self.current_path:
            self.current_path = parent
            self.add_to_history(parent)
            self.load_current_directory()
    
    def refresh(self):
        """Оновлення поточної директорії"""
        self.load_current_directory()
        if hasattr(self, 'status_label'):
            self.status_label.config(text="Оновлено")
    
    def on_window_resize(self, event):
        """Обробка зміни розміру вікна"""
        # Оновлюємо розміри панелей тільки якщо змінюється розмір головного вікна
        if event.widget == self.root:
            window_width = self.root.winfo_width()
            
            # Оновлюємо ширину бокових панелей
            panel_width = window_width // 6
            if hasattr(self, 'left_frame'):
                self.left_frame.configure(width=panel_width)
            if hasattr(self, 'right_frame'):
                self.right_frame.configure(width=panel_width)
    
    def on_canvas_resize(self, event):
        """Обробка зміни розміру canvas для перемалювання зображення"""
        if self.current_image and self.current_photo:
            # Перемалюємо зображення з новими розмірами
            self.redraw_image()
    
    def redraw_image(self):
        """Перемалювання зображення з поточними розмірами canvas"""
        if not self.current_image:
            return
            
        try:
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()
            
            if canvas_width <= 1 or canvas_height <= 1:
                return
            
            # Обчислюємо пропорції для масштабування
            img_width, img_height = self.current_image.size
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)
            
            # Масштабуємо зображення
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            resized_image = self.current_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Конвертуємо для tkinter
            photo = ImageTk.PhotoImage(resized_image)
            
            # Очищаємо canvas
            self.image_canvas.delete("all")
            
            # Розміщуємо зображення точно по центру
            x = canvas_width // 2
            y = canvas_height // 2
            
            self.image_canvas.create_image(x, y, anchor=tk.CENTER, image=photo)
            self.current_photo = photo  # Зберігаємо посилання
        
        except Exception as e:
            self.image_canvas.delete("all")
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()
            if canvas_width <= 1:
                canvas_width = 200
            if canvas_height <= 1:
                canvas_height = 150
            self.image_canvas.create_text(canvas_width // 2, canvas_height // 2, 
                                        text=f"Помилка перемалювання\nзображення:\n{e}", 
                                        anchor=tk.CENTER, fill="red")
    
    def create_status_bar(self):
        """Створення статусного рядка"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_frame, text="Готово", style="Path.TLabel")
        self.status_label.pack(side=tk.LEFT, padx=2)
        
        self.items_count_label = ttk.Label(self.status_frame, text="", style="Path.TLabel")
        self.items_count_label.pack(side=tk.RIGHT, padx=2)
    
    def add_to_history(self, path):
        """Додавання шляху до історії"""
        if self.history_index >= 0 and self.history_index < len(self.history):
            if self.history[self.history_index] != path:
                self.history = self.history[:self.history_index + 1]
                self.history.append(path)
                self.history_index += 1
        else:
            self.history.append(path)
            self.history_index = 0
    
    def on_address_enter(self, event):
        """Обробка введення адреси"""
        path = self.address_entry.get().strip()
        if os.path.exists(path) and os.path.isdir(path):
            self.current_path = path
            self.add_to_history(path)
            self.load_current_directory()
        else:
            messagebox.showerror("Помилка", "Невірний шлях")
    
    def on_search_change(self, *args):
        """Обробка зміни пошукового запиту"""
        self.load_current_directory()
    
    def on_filter_change(self, event):
        """Обробка зміни фільтра"""
        self.load_current_directory()
    
    def create_folder(self):
        """Створення нової папки"""
        name = simpledialog.askstring("Нова папка", "Введіть назву папки:")
        if name:
            try:
                new_path = os.path.join(self.current_path, name)
                os.makedirs(new_path, exist_ok=True)
                self.load_current_directory()
                if hasattr(self, 'status_label'):
                    self.status_label.config(text=f"Створено папку: {name}")
            except Exception as e:
                messagebox.showerror("Помилка", f"Не вдалося створити папку: {e}")
    
    def create_file(self):
        """Створення нового файлу"""
        name = simpledialog.askstring("Новий файл", "Введіть назву файлу:")
        if name:
            try:
                new_path = os.path.join(self.current_path, name)
                with open(new_path, 'w') as f:
                    pass
                self.load_current_directory()
                if hasattr(self, 'status_label'):
                    self.status_label.config(text=f"Створено файл: {name}")
            except Exception as e:
                messagebox.showerror("Помилка", f"Не вдалося створити файл: {e}")
    
    def get_selected_files(self):
        """Отримання вибраних файлів"""
        selection = self.file_tree.selection()
        files = []
        for item in selection:
            if os.path.exists(item):
                files.append(item)
        return files
    
    def copy_files(self, event=None):
        """Копіювання файлів"""
        files = self.get_selected_files()
        if files:
            self.clipboard_files = files.copy()
            self.clipboard_mode = "copy"
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"Скопійовано {len(files)} елементів")
    
    def cut_files(self, event=None):
        """Вирізання файлів"""
        files = self.get_selected_files()
        if files:
            self.clipboard_files = files.copy()
            self.clipboard_mode = "cut"
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"Вирізано {len(files)} елементів")
    
    def paste_files(self, event=None):
        """Вставка файлів"""
        if not self.clipboard_files:
            return
        
        for src_path in self.clipboard_files:
            try:
                filename = os.path.basename(src_path)
                dst_path = os.path.join(self.current_path, filename)
                
                if os.path.exists(dst_path):
                    # Додаємо номер до назви
                    name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(dst_path):
                        new_name = f"{name} ({counter}){ext}"
                        dst_path = os.path.join(self.current_path, new_name)
                        counter += 1
                
                if os.path.isdir(src_path):
                    shutil.copytree(src_path, dst_path)
                else:
                    shutil.copy2(src_path, dst_path)
                
                if self.clipboard_mode == "cut":
                    if os.path.isdir(src_path):
                        shutil.rmtree(src_path)
                    else:
                        os.remove(src_path)
                
            except Exception as e:
                messagebox.showerror("Помилка", f"Помилка при вставці {filename}: {e}")
        
        if self.clipboard_mode == "cut":
            self.clipboard_files = []
        
        self.load_current_directory()
        if hasattr(self, 'status_label'):
            self.status_label.config(text=f"Вставлено {len(self.clipboard_files)} елементів")
    
    def delete_files(self, event=None):
        """Видалення файлів"""
        files = self.get_selected_files()
        if not files:
            return
        
        result = messagebox.askyesno("Підтвердження", 
                                   f"Видалити {len(files)} елементів?")
        if result:
            for file_path in files:
                try:
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    else:
                        os.remove(file_path)
                except Exception as e:
                    messagebox.showerror("Помилка", f"Помилка при видаленні {os.path.basename(file_path)}: {e}")
            
            self.load_current_directory()
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"Видалено {len(files)} елементів")
    
    def rename_selected(self):
        """Перейменування вибраного файлу"""
        files = self.get_selected_files()
        if len(files) != 1:
            messagebox.showwarning("Попередження", "Виберіть один файл для перейменування")
            return
        
        old_path = files[0]
        old_name = os.path.basename(old_path)
        new_name = simpledialog.askstring("Перейменувати", "Нова назва:", initialvalue=old_name)
        
        if new_name and new_name != old_name:
            try:
                new_path = os.path.join(self.current_path, new_name)
                os.rename(old_path, new_path)
                self.load_current_directory()
                if hasattr(self, 'status_label'):
                    self.status_label.config(text=f"Перейменовано: {old_name} → {new_name}")
            except Exception as e:
                messagebox.showerror("Помилка", f"Помилка при перейменуванні: {e}")
    
    def open_selected(self):
        """Відкриття вибраного файлу"""
        files = self.get_selected_files()
        if len(files) != 1:
            return
        
        file_path = files[0]
        if os.path.isdir(file_path):
            self.current_path = file_path
            self.add_to_history(file_path)
            self.load_current_directory()
        else:
            try:
                if platform.system() == "Windows":
                    os.startfile(file_path)
                else:
                    subprocess.run(["xdg-open", file_path])
            except Exception as e:
                messagebox.showerror("Помилка", f"Не вдалося відкрити файл: {e}")
    
    def open_with(self):
        """Відкриття з..."""
        files = self.get_selected_files()
        if len(files) != 1:
            return
        
        file_path = files[0]
        if os.path.isdir(file_path):
            return
        
        # Простий список програм (можна розширити)
        programs = ["notepad.exe", "wordpad.exe", "mspaint.exe"]
        program = simpledialog.askstring("Відкрити з", "Введіть назву програми:")
        if program:
            try:
                subprocess.run([program, file_path])
            except Exception as e:
                messagebox.showerror("Помилка", f"Не вдалося відкрити з {program}: {e}")
    
    def show_properties(self):
        """Показ властивостей файлу"""
        files = self.get_selected_files()
        if len(files) != 1:
            return
        
        file_path = files[0]
        try:
            stat = os.stat(file_path)
            size = self.format_size(stat.st_size)
            modified = datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            info = f"Назва: {os.path.basename(file_path)}\n"
            info += f"Тип: {'Папка' if os.path.isdir(file_path) else 'Файл'}\n"
            info += f"Розмір: {size}\n"
            info += f"Змінено: {modified}\n"
            info += f"Шлях: {file_path}"
            
            messagebox.showinfo("Властивості", info)
        except Exception as e:
            messagebox.showerror("Помилка", f"Помилка при отриманні властивостей: {e}")
    
    def select_all(self, event=None):
        """Вибір всіх файлів"""
        for item in self.file_tree.get_children():
            self.file_tree.selection_add(item)
    
    def show_context_menu(self, event):
        """Показ контекстного меню"""
        self.context_menu.post(event.x_root, event.y_root)
    
    def update_status_bar(self):
        """Оновлення статусного рядка"""
        if not hasattr(self, 'items_count_label'):
            return
            
        total_items = len(self.file_tree.get_children())
        selected_items = len(self.file_tree.selection())
        
        if selected_items > 0:
            self.items_count_label.config(text=f"Вибрано: {selected_items} з {total_items}")
        else:
            self.items_count_label.config(text=f"Всього: {total_items} елементів")

def main():
    root = tk.Tk()
    app = FileExplorer(root)
    root.mainloop()

if __name__ == "__main__":
    main() 