# Файловий провідник на Python

Власний файловий провідник, створений за допомогою Python та tkinter.

## Особливості

- **Три панелі**: дерево директорій (ліворуч), вміст поточної директорії (по центру), попередній перегляд файлів (праворуч)
- **Підтримка текстових файлів**: перегляд .txt файлів
- **Підтримка зображень**: перегляд всіх основних форматів зображень (PNG, JPG, GIF, BMP, TIFF, та багато інших)
- **Навігація**: кнопк<PERSON> "Вгору" та "Оновити"
- **Інформація про файли**: розмір, дата зміни, тип файлу

## Встановлення

1. Переконайтеся, що у вас встановлений Python 3.6 або новіше
2. Встановіть залежності:
   ```bash
   pip install -r requirements.txt
   ```

## Запуск

```bash
python file_explorer.py
```

## Підтримувані формати зображень

Програма підтримує всі формати зображень, які може обробити бібліотека Pillow, включаючи:
- 3FR, AAI, AI, ART, ARW, AVS
- BGR, BGRA, BIE, BMP
- CAL, CALS, CANVAS, CIN, CMYK, CMYKA
- CR2, CRW, CUR
- DCM, DCR, DCX, DDS, DIB, DJVU, DNG, DPX
- EPDF, EPS, EPSF, EPSI, EPT, ERF, EXR
- FAX, FITS, FRACTAL, FTS
- G3, GIF, GIF87, GRAY, GROUP4
- HDR, HRZ
- ICB, ICO, ICON, IIQ
- JBG, JBIG, JNG, JNX, JP2, JPE, JPEG, JPG, JSON
- K25, KDC
- MAC, MAT, MEF, MIFF, MNG, MONO, MPC, MRW, MTV
- NEF, NRW
- ORF, OTB, OTF
- PAL, PALM, PAM, PBM, PCD, PCDS, PCT, PCX, PDB, PDF, PDFA, PEF, PES, PFM, PGM, PICON, PICT, PIX, PJPEG, PLASMA, PNG, PNM, PS, PSB, PSD, PTIF, PWP
- R, RAF, RAS, RAW, RGB, RGBA, RGBO, RGF, RLA, RLE, RMF, RW2
- SFW, SGI, SIX, SIXEL, SR2, SRF, STEGANO, SUN
- TGA, TIF, TIFF, TIFF64, TILE
- UYVY
- VDA, VICAR, VIFF, VIPS, VST
- WBMP, WEBP, WPG
- XBM, XCF, XWD, X3F

## Використання

1. **Навігація по дереву директорій**: клікніть на директорію в лівій панелі
2. **Перегляд файлів**: клікніть на файл у центральній панелі для попереднього перегляду
3. **Відкриття директорій**: подвійний клік на директорію у центральній панелі
4. **Навігація**: використовуйте кнопки "Вгору" та "Оновити"

## Системні вимоги

- Python 3.6+
- tkinter (зазвичай входить до стандартної установки Python)
- Pillow (для обробки зображень) 